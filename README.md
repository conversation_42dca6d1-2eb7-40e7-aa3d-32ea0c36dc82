# Taxi Userbot - Telethon Bot

O'zbek kril yozuvida ishlaydigon Telegram bot. Foydalanuvchilar subscription asosida bot xizmatlaridan foydalanishlari mumkin.

## Xususiyatlar

- ✅ O'zbek kril yozuvida to'liq qo'llab-quvvatlash
- ✅ Subscription asosida foydalanuvchilarni boshqarish
- ✅ Django admin panel orqali subscription boshqarish
- ✅ Telethon kutubxonasi asosida qurilgan
- ✅ Foydalanuvchi-do'st interfeys

## Komandalar

- `/start` - Botni ishga tushirish va subscription holatini tekshirish
- `/help` - Yordam va komandalar ro'yxati
- `/status` - Joriy subscription holatini ko'rish

## O'rnatish

1. Repository ni klonlash:
```bash
git clone <repository_url>
cd taxi_userbot
```

2. Virtual environment yaratish:
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# yoki
venv\Scripts\activate  # Windows
```

3. Dependency larni o'rnatish:
```bash
pip install -r requirements.txt
```

4. Environment variables sozlash:
```bash
cp .env.example .env
# .env faylini tahrirlang va kerakli qiymatlarni kiriting
```

5. Database migratsiya:
```bash
python manage.py migrate
```

6. Admin user yaratish:
```bash
python manage.py createsuperuser
```

7. Botni ishga tushirish:
```bash
python bot.py
```

## Konfiguratsiya

`.env` faylida quyidagi parametrlarni sozlang:

- `BOT_TOKEN` - Telegram bot token (@BotFather dan olinadi)
- `API_ID` - Telegram API ID (my.telegram.org dan)
- `API_HASH` - Telegram API Hash (my.telegram.org dan)
- `ADMIN_USERNAME` - Admin username (@username formatida)

## Admin Panel

Django admin panel orqali subscription larni boshqarishingiz mumkin:

1. `http://localhost:8000/admin/` ga kiring
2. Subscription bo'limida foydalanuvchilarni ko'ring
3. Subscription larni faollashtirish/o'chirish
4. Obuna muddatini uzaytirish

## Subscription Boshqarish

Admin panelda quyidagi amallarni bajarishingiz mumkin:

- **Obunani faollashtirish** - Tanlangan foydalanuvchilar uchun obunani yoqish
- **Obunani o'chirish** - Tanlangan foydalanuvchilar uchun obunani o'chirish  
- **30 kunga uzaytirish** - Obuna muddatini 30 kunga uzaytirish

## Loyiha Strukturasi

```
taxi_userbot/
├── userbot/
│   ├── external/
│   │   └── telethon/
│   │       ├── __init__.py
│   │       ├── config.py
│   │       └── bot.py
│   ├── models.py
│   ├── admin.py
│   └── ...
├── bot.py
├── requirements.txt
├── .env.example
└── README.md
```

## Texnik Ma'lumotlar

- **Framework**: Django 5.1.5
- **Bot Library**: Telethon 1.38.1
- **Database**: SQLite (default), PostgreSQL qo'llab-quvvatlanadi
- **Python**: 3.8+

## Yordam

Muammolar yoki savollar bo'lsa, admin bilan bog'laning yoki GitHub Issues bo'limida muammo yarating.
