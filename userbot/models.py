from asgiref.sync import sync_to_async
from datetime import datetime, timedelta

from django.db import models
from django.utils import timezone


ADMIN = 7713527532


class Session(models.Model):
    """
    Model to store session data.
    """
    api_id = models.CharField(max_length=255, unique=True)
    api_hash = models.CharField(max_length=255, unique=True)
    session_name = models.CharField(max_length=255, unique=True)
    number = models.Char<PERSON>ield(max_length=15, unique=True)
    two_fa_password = models.Char<PERSON>ield(max_length=255, null=True, blank=True)
    is_active = models.Bo<PERSON>anField(default=False)

    def __str__(self):
        return str(self.session_name)

    @classmethod
    def get_by_session_name(cls, session_name: str):
        """
        Get session by session name.
        """
        return cls.objects.get(session_name=session_name)

    def mark_as_active(self):
        """
        Mark the session as active.
        """
        self.is_active = True
        self.save()

    def save(self, *args, **kwargs):
        """
        Save the session.
        """
        self.session_name = self.session_name.lower()
        self.number = self.number.replace("+", "")
        super().save(*args, **kwargs)


class TelegramSession(models.Model):
    """
    Django session.
    """
    session_name = models.CharField(max_length=255, unique=True)
    session_data = models.BinaryField(null=True, blank=True)

    def __str__(self):
        return self.session_name


class Subscription(models.Model):
    """
    Foydalanuvchi subscription modeli.
    """
    user_id = models.BigIntegerField(unique=True, verbose_name="Telegram User ID")
    username = models.CharField(max_length=255, null=True, blank=True, verbose_name="Username")
    first_name = models.CharField(max_length=255, null=True, blank=True, verbose_name="Ism")
    last_name = models.CharField(max_length=255, null=True, blank=True, verbose_name="Familiya")
    phone_number = models.CharField(max_length=20, null=True, blank=True, verbose_name="Telefon raqam")

    # Subscription ma'lumotlari
    is_active = models.BooleanField(default=False, verbose_name="Faol")
    start_date = models.DateTimeField(null=True, blank=True, verbose_name="Boshlanish sanasi")
    end_date = models.DateTimeField(null=True, blank=True, verbose_name="Tugash sanasi")

    # Qo'shimcha ma'lumotlar
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="Yaratilgan sana")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="Yangilangan sana")
    notes = models.TextField(null=True, blank=True, verbose_name="Izohlar")

    class Meta:
        verbose_name = "Subscription"
        verbose_name_plural = "Subscriptions"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.user_id} - {self.first_name or 'No name'}"

    @property
    def is_expired(self):
        """Subscription muddati tugaganligini tekshirish."""
        if not self.end_date:
            return not self.is_active
        return timezone.now() > self.end_date

    @property
    def days_remaining(self):
        """Qolgan kunlar soni."""
        if not self.end_date:
            return None
        remaining = self.end_date - timezone.now()
        return remaining.days if remaining.days > 0 else 0

    @classmethod
    @sync_to_async
    def get_user_subscription(cls, user_id: int):
        """Foydalanuvchi subscription'ini olish."""
        try:
            return cls.objects.get(user_id=user_id)
        except cls.DoesNotExist:
            return None

    @classmethod
    @sync_to_async
    def create_or_update_user(cls, user_id: int, username: str = None,
                             first_name: str = None, last_name: str = None):
        """Foydalanuvchini yaratish yoki yangilash."""
        subscription, created = cls.objects.get_or_create(
            user_id=user_id,
            defaults={
                'username': username,
                'first_name': first_name,
                'last_name': last_name,
                'is_active': False
            }
        )

        if not created:
            # Mavjud foydalanuvchi ma'lumotlarini yangilash
            subscription.username = username or subscription.username
            subscription.first_name = first_name or subscription.first_name
            subscription.last_name = last_name or subscription.last_name
            subscription.save()

        return subscription
