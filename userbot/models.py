from asgiref.sync import sync_to_async

from django.db import models


ADMIN = 7713527532


class Session(models.Model):
    """
    Model to store session data.
    """
    api_id = models.CharField(max_length=255, unique=True)
    api_hash = models.Char<PERSON>ield(max_length=255, unique=True)
    session_name = models.CharField(max_length=255, unique=True)
    number = models.Char<PERSON>ield(max_length=15, unique=True)
    two_fa_password = models.Char<PERSON>ield(max_length=255, null=True, blank=True)
    is_active = models.BooleanField(default=False)

    def __str__(self):
        return str(self.session_name)

    @classmethod
    def get_by_session_name(cls, session_name: str):
        """
        Get session by session name.
        """
        return cls.objects.get(session_name=session_name)

    def mark_as_active(self):
        """
        Mark the session as active.
        """
        self.is_active = True
        self.save()

    def save(self, *args, **kwargs):
        """
        Save the session.
        """
        self.session_name = self.session_name.lower()
        self.number = self.number.replace("+", "")
        super().save(*args, **kwargs)


class TelegramSession(models.Model):
    """
    Django session.
    """
    session_name = models.Char<PERSON>ield(max_length=255, unique=True)
    session_data = models.BinaryField(null=True, blank=True)

    def __str__(self):
        return self.session_name
