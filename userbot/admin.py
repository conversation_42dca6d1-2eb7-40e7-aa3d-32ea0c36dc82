from django.contrib import admin
from django.db.models import Model
from django.core.cache import cache
from django.http import HttpRequest, HttpResponse
from django.template.response import TemplateResponse
from django.utils import timezone
from datetime import timed<PERSON><PERSON>

from unfold.admin import ModelAdmin

from userbot.models import Session, Subscription


class SessionAdmin(ModelAdmin):
    """
    Admin class for Session model.
    """
    list_display = ("session_name", "number", "is_active")
    list_filter = ("is_active",)
    search_fields = ("session_name", "number")
    ordering = ("-id",)
    readonly_fields = ("is_active",)

    def get_fields(self, request, obj=None):
        fields = super().get_fields(request, obj)
        if not obj:
            fields = [field for field in fields if field != "is_active"]
        return fields

    def response_add(
        self,
        request: HttpRequest,
        obj: Model,
        post_url_continue: str | None = None,
    ) -> HttpResponse:
        # Eski userbot service o'chirildi - Session model endi ishlatilmaydi
        # Faqat Subscription model ishlatiladi
        return super().response_add(request, obj, post_url_continue)


class SubscriptionAdmin(ModelAdmin):
    """
    Admin class for Subscription model.
    """
    list_display = ("user_id", "first_name", "username", "is_active", "start_date", "end_date", "days_remaining_display")
    list_filter = ("is_active", "created_at", "start_date", "end_date")
    search_fields = ("user_id", "username", "first_name", "last_name", "phone_number")
    ordering = ("-created_at",)
    readonly_fields = ("created_at", "updated_at", "days_remaining_display", "is_expired_display")

    fieldsets = (
        ("Foydalanuvchi ma'lumotlari", {
            'fields': ('user_id', 'username', 'first_name', 'last_name', 'phone_number')
        }),
        ("Subscription ma'lumotlari", {
            'fields': ('is_active', 'start_date', 'end_date', 'days_remaining_display', 'is_expired_display')
        }),
        ("Qo'shimcha", {
            'fields': ('notes', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def days_remaining_display(self, obj):
        """Qolgan kunlarni ko'rsatish."""
        days = obj.days_remaining
        if days is None:
            return "Cheksiz"
        return f"{days} kun"
    days_remaining_display.short_description = "Qolgan kunlar"

    def is_expired_display(self, obj):
        """Muddati tugaganligini ko'rsatish."""
        return "Ha" if obj.is_expired else "Yo'q"
    is_expired_display.short_description = "Muddati tugagan"
    is_expired_display.boolean = True

    actions = ['activate_subscription', 'deactivate_subscription', 'extend_subscription_30_days']

    def activate_subscription(self, request, queryset):
        """Obunani faollashtirish."""
        updated = 0
        for subscription in queryset:
            if not subscription.is_active:
                subscription.is_active = True
                if not subscription.start_date:
                    subscription.start_date = timezone.now()
                subscription.save()
                updated += 1

        self.message_user(request, f"{updated} ta obuna faollashtirildi.")
    activate_subscription.short_description = "Tanlangan obunalarni faollashtirish"

    def deactivate_subscription(self, request, queryset):
        """Obunani o'chirish."""
        updated = queryset.update(is_active=False)
        self.message_user(request, f"{updated} ta obuna o'chirildi.")
    deactivate_subscription.short_description = "Tanlangan obunalarni o'chirish"

    def extend_subscription_30_days(self, request, queryset):
        """Obunani 30 kunga uzaytirish."""
        updated = 0
        for subscription in queryset:
            if subscription.end_date:
                subscription.end_date += timedelta(days=30)
            else:
                subscription.end_date = timezone.now() + timedelta(days=30)

            if not subscription.start_date:
                subscription.start_date = timezone.now()

            subscription.is_active = True
            subscription.save()
            updated += 1

        self.message_user(request, f"{updated} ta obuna 30 kunga uzaytirildi.")
    extend_subscription_30_days.short_description = "Obunani 30 kunga uzaytirish"


admin.site.register(Session, SessionAdmin)
admin.site.register(Subscription, SubscriptionAdmin)
