"""
Telethon bot as<PERSON><PERSON>y fay<PERSON>.
"""
import os
import django
import asyncio
import logging

from telethon import TelegramClient, events, Button

# Django sozlamalarini yuklash
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from userbot.models import Subscription
from userbot.external.telethon.config import (
    BOT_TOKEN, API_ID, API_HASH, SESSION_NAME, 
    ADMIN_ID, ADMIN_USERNAME, MESSAGES, BUTTONS
)

# Logging sozlash
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Bot yaratish
bot = TelegramClient(SESSION_NAME, API_ID, API_HASH).start(bot_token=BOT_TOKEN)


async def check_subscription(user_id: int) -> dict:
    """
    Foydalanuvchi subscription holatini tekshirish.
    """
    try:
        subscription = await Subscription.get_user_subscription(user_id)
        
        if not subscription:
            return {
                "has_subscription": False,
                "is_active": False,
                "message": "no_subscription"
            }
        
        if subscription.is_expired or not subscription.is_active:
            return {
                "has_subscription": True,
                "is_active": False,
                "subscription": subscription,
                "message": "subscription_expired"
            }
        
        return {
            "has_subscription": True,
            "is_active": True,
            "subscription": subscription,
            "message": "subscription_active"
        }
    
    except Exception as e:
        logger.error(f"Subscription tekshirishda xatolik: {e}")
        return {
            "has_subscription": False,
            "is_active": False,
            "message": "error"
        }


def get_contact_admin_button():
    """
    Admin bilan bog'lanish tugmasini yaratish.
    """
    return [Button.url(
        BUTTONS["contact_admin"], 
        f"https://t.me/{ADMIN_USERNAME.replace('@', '')}"
    )]


@bot.on(events.NewMessage(pattern='/start'))
async def start_handler(event):
    """
    /start komandasi uchun handler.
    """
    user = await event.get_sender()
    user_id = user.id
    
    logger.info(f"Start komandasi: {user_id} - {user.first_name}")
    
    # Foydalanuvchi ma'lumotlarini saqlash/yangilash
    try:
        await Subscription.create_or_update_user(
            user_id=user_id,
            username=user.username,
            first_name=user.first_name,
            last_name=user.last_name
        )
    except Exception as e:
        logger.error(f"Foydalanuvchi ma'lumotlarini saqlashda xatolik: {e}")
    
    # Salomlashish xabari
    await event.respond(MESSAGES["welcome"])
    
    # Subscription holatini tekshirish
    await asyncio.sleep(1)  # Biroz kutish
    subscription_status = await check_subscription(user_id)
    
    if subscription_status["message"] == "error":
        await event.respond(MESSAGES["error"])
        return
    
    if not subscription_status["is_active"]:
        # Obuna yo'q yoki muddati tugagan
        message_text = MESSAGES[subscription_status["message"]].format(
            admin_username=ADMIN_USERNAME
        )
        await event.respond(
            message_text,
            buttons=get_contact_admin_button()
        )
    else:
        # Obuna faol
        subscription = subscription_status["subscription"]
        end_date = subscription.end_date.strftime("%d.%m.%Y") if subscription.end_date else "Чексиз"
        days_remaining = subscription.days_remaining if subscription.days_remaining else "Чексиз"
        
        message_text = MESSAGES["subscription_active"].format(
            end_date=end_date,
            days_remaining=days_remaining
        )
        await event.respond(message_text)


@bot.on(events.NewMessage(pattern='/help'))
async def help_handler(event):
    """
    /help komandasi uchun handler.
    """
    help_text = MESSAGES["help_text"].format(admin_username=ADMIN_USERNAME)
    await event.respond(help_text)


@bot.on(events.NewMessage(pattern='/status'))
async def status_handler(event):
    """
    /status komandasi uchun handler - obuna holatini ko'rsatish.
    """
    user = await event.get_sender()
    user_id = user.id
    
    subscription_status = await check_subscription(user_id)
    
    if subscription_status["message"] == "error":
        await event.respond(MESSAGES["error"])
        return
    
    if not subscription_status["is_active"]:
        message_text = MESSAGES[subscription_status["message"]].format(
            admin_username=ADMIN_USERNAME
        )
        await event.respond(
            message_text,
            buttons=get_contact_admin_button()
        )
    else:
        subscription = subscription_status["subscription"]
        end_date = subscription.end_date.strftime("%d.%m.%Y") if subscription.end_date else "Чексиз"
        days_remaining = subscription.days_remaining if subscription.days_remaining else "Чексиз"
        start_date = subscription.start_date.strftime("%d.%m.%Y") if subscription.start_date else "Номаълум"
        created_at = subscription.created_at.strftime("%d.%m.%Y %H:%M")
        
        status_text = MESSAGES["status_active"].format(
            user_id=user_id,
            start_date=start_date,
            end_date=end_date,
            days_remaining=days_remaining,
            created_at=created_at
        )
        
        await event.respond(status_text)


@bot.on(events.NewMessage)
async def other_messages_handler(event):
    """
    Boshqa xabarlar uchun handler.
    """
    # Komandalarni o'tkazib yuborish
    if event.message.text and event.message.text.startswith('/'):
        return
    
    user = await event.get_sender()
    user_id = user.id
    
    # Subscription holatini tekshirish
    subscription_status = await check_subscription(user_id)
    
    if not subscription_status["is_active"]:
        # Obuna yo'q yoki muddati tugagan
        message_text = MESSAGES[subscription_status["message"]].format(
            admin_username=ADMIN_USERNAME
        )
        await event.respond(
            message_text,
            buttons=get_contact_admin_button()
        )
        return
    
    # Bu yerda bot funksiyalarini qo'shishingiz mumkin
    await event.respond(MESSAGES["bot_functions"])


async def main():
    """
    Botni ishga tushirish.
    """
    logger.info("Bot ishga tushmoqda...")
    
    # Bot ma'lumotlarini olish
    me = await bot.get_me()
    logger.info(f"Bot muvaffaqiyatli ishga tushdi: @{me.username}")
    
    # Bot ishlashini davom ettirish
    await bot.run_until_disconnected()


def run_bot():
    """
    Botni ishga tushirish funksiyasi.
    """
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Bot to'xtatildi")
    except Exception as e:
        logger.error(f"Bot ishida xatolik: {e}")


if __name__ == "__main__":
    run_bot()
