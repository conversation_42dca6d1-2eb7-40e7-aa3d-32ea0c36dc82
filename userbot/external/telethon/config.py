"""
Telethon bot konfiguratsiyasi.
"""
import os
from django.conf import settings
from dotenv import load_dotenv

# .env faylini yuklash
load_dotenv()

# Bot konfiguratsiyasi
BOT_TOKEN = os.getenv("BOT_TOKEN", "YOUR_BOT_TOKEN_HERE")
API_ID = int(os.getenv("API_ID", "0"))
API_HASH = os.getenv("API_HASH", "YOUR_API_HASH_HERE")

# Admin ma'lumotlari
ADMIN_ID = 7713527532  # Admin user ID
ADMIN_USERNAME = "@admin"  # Admin username

# Bot sozlamalari
BOT_NAME = "Taxi Userbot"
SESSION_NAME = "taxi_bot_session"

# O'zbek tilida xabarlar
MESSAGES = {
    "welcome": """🚖 Ассалому алайкум! Taxi Userbot хизматига хуш келибсиз!

Сизнинг обуна ҳолатини текширяпмиз...""",
    
    "no_subscription": """❌ Сизда фаол обуна мавжуд эмас!

Бот хизматларидан фойдаланиш учун админ билан боғланинг:
👤 Админ: {admin_username}

📞 Ёки қуйидаги тугмани босинг:""",
    
    "subscription_active": """✅ Сизнинг обунангиз фаол!

🗓 Обуна тугаш санаси: {end_date}
⏰ Қолган кунлар: {days_remaining}

Бот хизматларидан фойдаланишингиз мумкин.""",
    
    "subscription_expired": """⚠️ Сизнинг обунангиз муддати тугаган!

Админ билан боғланиб, обунани янгиланг:
👤 Админ: {admin_username}

📞 Ёки қуйидаги тугмани босинг:""",
    
    "contact_admin": "👤 Админ билан боғланиш",
    
    "error": "❌ Хатолик юз берди. Илтимос, қайтадан уриниб кўринг.",
    
    "processing": "⏳ Илтимос, кутинг...",
    
    "help_text": """🤖 Бот командалар рўйхати:

/start - Ботни ишга тушириш
/help - Ёрдам
/status - Обуна ҳолатини кўриш

📞 Ёрдам учун админ билан боғланинг: {admin_username}""",
    
    "status_active": """📊 Обуна ҳолати:

✅ Статус: Фаол
👤 Фойдаланувчи ID: {user_id}
📅 Бошланиш санаси: {start_date}
📅 Тугаш санаси: {end_date}
⏰ Қолган кунлар: {days_remaining}
📝 Яратилган: {created_at}""",
    
    "bot_functions": "✅ Сизнинг обунангиз фаол! Бот функциялари тез орада қўшилади."
}

# Inline keyboard tugmalari
BUTTONS = {
    "contact_admin": "👤 Админ билан боғланиш"
}
