"""
Pyrogram bot as<PERSON><PERSON><PERSON> fay<PERSON>.
"""
import os
import django
import asyncio
import logging
from datetime import datetime

from pyrogram import Client, filters
from pyrogram.types import Message, InlineKeyboardMarkup, InlineKeyboardButton, CallbackQuery

# Django so<PERSON> yuklash
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from userbot.models import Subscription
from userbot.external.pyrogram.config import (
    BOT_TOKEN, API_ID, API_HASH, SESSION_NAME, 
    ADMIN_ID, ADMIN_USERNAME, MESSAGES, BUTTONS
)

# Logging sozlash
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Bot yaratish
app = Client(
    SESSION_NAME,
    api_id=API_ID,
    api_hash=API_HASH,
    bot_token=BOT_TOKEN
)


async def check_subscription(user_id: int) -> dict:
    """
    Foydalanuvchi subscription holatini tekshirish.
    """
    try:
        subscription = await Subscription.get_user_subscription(user_id)
        
        if not subscription:
            return {
                "has_subscription": False,
                "is_active": False,
                "message": "no_subscription"
            }
        
        if subscription.is_expired or not subscription.is_active:
            return {
                "has_subscription": True,
                "is_active": False,
                "subscription": subscription,
                "message": "subscription_expired"
            }
        
        return {
            "has_subscription": True,
            "is_active": True,
            "subscription": subscription,
            "message": "subscription_active"
        }
    
    except Exception as e:
        logger.error(f"Subscription tekshirishda xatolik: {e}")
        return {
            "has_subscription": False,
            "is_active": False,
            "message": "error"
        }


def get_contact_admin_keyboard():
    """
    Admin bilan bog'lanish tugmasini yaratish.
    """
    return InlineKeyboardMarkup([
        [InlineKeyboardButton(
            BUTTONS["contact_admin"], 
            url=f"https://t.me/{ADMIN_USERNAME.replace('@', '')}"
        )]
    ])


@app.on_message(filters.command("start") & filters.private)
async def start_handler(client: Client, message: Message):
    """
    /start komandasi uchun handler.
    """
    user = message.from_user
    user_id = user.id
    
    logger.info(f"Start komandasi: {user_id} - {user.first_name}")
    
    # Foydalanuvchi ma'lumotlarini saqlash/yangilash
    try:
        await Subscription.create_or_update_user(
            user_id=user_id,
            username=user.username,
            first_name=user.first_name,
            last_name=user.last_name
        )
    except Exception as e:
        logger.error(f"Foydalanuvchi ma'lumotlarini saqlashda xatolik: {e}")
    
    # Salomlashish xabari
    await message.reply_text(MESSAGES["welcome"])
    
    # Subscription holatini tekshirish
    await asyncio.sleep(1)  # Biroz kutish
    subscription_status = await check_subscription(user_id)
    
    if subscription_status["message"] == "error":
        await message.reply_text(MESSAGES["error"])
        return
    
    if not subscription_status["is_active"]:
        # Obuna yo'q yoki muddati tugagan
        message_text = MESSAGES[subscription_status["message"]].format(
            admin_username=ADMIN_USERNAME
        )
        await message.reply_text(
            message_text,
            reply_markup=get_contact_admin_keyboard()
        )
    else:
        # Obuna faol
        subscription = subscription_status["subscription"]
        end_date = subscription.end_date.strftime("%d.%m.%Y") if subscription.end_date else "Cheksiz"
        days_remaining = subscription.days_remaining if subscription.days_remaining else "Cheksiz"
        
        message_text = MESSAGES["subscription_active"].format(
            end_date=end_date,
            days_remaining=days_remaining
        )
        await message.reply_text(message_text)


@app.on_message(filters.private & ~filters.command("start"))
async def other_messages_handler(client: Client, message: Message):
    """
    Boshqa xabarlar uchun handler.
    """
    user_id = message.from_user.id
    
    # Subscription holatini tekshirish
    subscription_status = await check_subscription(user_id)
    
    if not subscription_status["is_active"]:
        # Obuna yo'q yoki muddati tugagan
        message_text = MESSAGES[subscription_status["message"]].format(
            admin_username=ADMIN_USERNAME
        )
        await message.reply_text(
            message_text,
            reply_markup=get_contact_admin_keyboard()
        )
        return
    
    # Bu yerda bot funksiyalarini qo'shishingiz mumkin
    await message.reply_text("✅ Sizning obunangiz faol! Bot funksiyalari tez orada qo'shiladi.")


@app.on_callback_query()
async def callback_handler(client: Client, callback_query: CallbackQuery):
    """
    Callback query handler.
    """
    data = callback_query.data
    user_id = callback_query.from_user.id

    if data == "contact_admin":
        await callback_query.answer("Admin bilan bog'lanish uchun tugmani bosing!", show_alert=True)
    else:
        await callback_query.answer("Noma'lum komanda!", show_alert=True)


@app.on_message(filters.command("help") & filters.private)
async def help_handler(client: Client, message: Message):
    """
    /help komandasi uchun handler.
    """
    help_text = """🤖 Bot komandalar ro'yxati:

/start - Botni ishga tushirish
/help - Yordam
/status - Obuna holatini ko'rish

📞 Yordam uchun admin bilan bog'laning: {admin_username}"""

    await message.reply_text(help_text.format(admin_username=ADMIN_USERNAME))


@app.on_message(filters.command("status") & filters.private)
async def status_handler(client: Client, message: Message):
    """
    /status komandasi uchun handler - obuna holatini ko'rsatish.
    """
    user_id = message.from_user.id

    subscription_status = await check_subscription(user_id)

    if subscription_status["message"] == "error":
        await message.reply_text(MESSAGES["error"])
        return

    if not subscription_status["is_active"]:
        message_text = MESSAGES[subscription_status["message"]].format(
            admin_username=ADMIN_USERNAME
        )
        await message.reply_text(
            message_text,
            reply_markup=get_contact_admin_keyboard()
        )
    else:
        subscription = subscription_status["subscription"]
        end_date = subscription.end_date.strftime("%d.%m.%Y") if subscription.end_date else "Cheksiz"
        days_remaining = subscription.days_remaining if subscription.days_remaining else "Cheksiz"

        status_text = f"""📊 Obuna holati:

✅ Status: Faol
👤 Foydalanuvchi ID: {user_id}
📅 Boshlanish sanasi: {subscription.start_date.strftime("%d.%m.%Y") if subscription.start_date else "Noma'lum"}
📅 Tugash sanasi: {end_date}
⏰ Qolgan kunlar: {days_remaining}
📝 Yaratilgan: {subscription.created_at.strftime("%d.%m.%Y %H:%M")}"""

        await message.reply_text(status_text)


async def main():
    """
    Botni ishga tushirish.
    """
    logger.info("Bot ishga tushmoqda...")
    await app.start()
    logger.info("Bot muvaffaqiyatli ishga tushdi!")
    
    # Bot ishlashini davom ettirish
    await app.idle()


def run_bot():
    """
    Botni ishga tushirish funksiyasi.
    """
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Bot to'xtatildi")
    except Exception as e:
        logger.error(f"Bot ishida xatolik: {e}")


if __name__ == "__main__":
    run_bot()
