"""
Pyrogram bot konfiguratsiyasi.
"""
import os
from django.conf import settings
from dotenv import load_dotenv

# .env faylini yuklash
load_dotenv()

# Bot konfiguratsiyasi
BOT_TOKEN = os.getenv("BOT_TOKEN", "YOUR_BOT_TOKEN_HERE")
API_ID = int(os.getenv("API_ID", "0"))
API_HASH = os.getenv("API_HASH", "YOUR_API_HASH_HERE")

# Admin ma'lumotlari
ADMIN_ID = 7713527532  # Admin user ID
ADMIN_USERNAME = "@admin"  # Admin username

# Bot sozlamalari
BOT_NAME = "Taxi Userbot"
SESSION_NAME = "taxi_bot_session"

# O'zbek tilida xabarlar
MESSAGES = {
    "welcome": """🚖 Assalomu alaykum! Taxi Userbot xizmatiga xush kelibsiz!

Sizning obuna holatini tekshiryapmiz...""",
    
    "no_subscription": """❌ Sizda faol obuna mavjud emas!

Bot xizmatlaridan foydalanish uchun admin bilan bog'laning:
👤 Admin: {admin_username}

📞 Yoki quyidagi tugmani bosing:""",
    
    "subscription_active": """✅ Sizning obunangiz faol!

🗓 Obuna tugash sanasi: {end_date}
⏰ Qolgan kunlar: {days_remaining}

Bot xizmatlaridan foydalanishingiz mumkin.""",
    
    "subscription_expired": """⚠️ Sizning obunangiz muddati tugagan!

Admin bilan bog'lanib, obunani yangilang:
👤 Admin: {admin_username}

📞 Yoki quyidagi tugmani bosing:""",
    
    "contact_admin": "👤 Admin bilan bog'lanish",
    
    "error": "❌ Xatolik yuz berdi. Iltimos, qaytadan urinib ko'ring.",
    
    "processing": "⏳ Iltimos, kuting..."
}

# Inline keyboard tugmalari
BUTTONS = {
    "contact_admin": "👤 Admin bilan bog'lanish"
}
